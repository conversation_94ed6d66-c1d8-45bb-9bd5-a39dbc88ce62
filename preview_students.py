#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预览前10个学员的API参数
"""

import batch_create_students
import json

def main():
    """
    主函数：显示前10个学员的API创建参数
    """
    # 静默解析学员数据
    import sys
    from io import StringIO
    
    # 重定向标准输出以静默解析过程
    old_stdout = sys.stdout
    sys.stdout = StringIO()
    
    try:
        students_data, failed_files = batch_create_students.main()
    finally:
        sys.stdout = old_stdout
    
    if not students_data:
        print("没有成功解析的学员数据")
        return
    
    print(f"=== 成功解析 {len(students_data)} 个学员，预览前10个学员的API创建参数 ===\n")
    
    # 只显示前10个学员
    preview_count = min(10, len(students_data))
    
    for i in range(preview_count):
        student = students_data[i]
        print(f"学员 {i+1}: {student['name']}")
        print(f"课程: {student['courseName']} (ID: {student['courseId']})")
        print(f"教练: {student['coachName']} (ID: {student['coachId']})")
        print(f"课时记录: {student['classRecords']} 条")
        print(f"入学日期: {student['enrollDate']}")
        
        # 构建API调用参数
        api_payload = {
            "studentInfo": {
                "name": student['name'],
                "gender": student['gender'],
                "age": student['age'],
                "phone": student['phone'],
                "email": student['email'],
                "address": student['address'],
                "parentName": student['parentName'],
                "parentPhone": student['parentPhone'],
                "campusId": student['campusId']
            },
            "courseInfoList": [
                {
                    "courseId": student['courseId'],
                    "courseName": student['courseName'],
                    "coachId": student['coachId'],
                    "coachName": student['coachName'],
                    "enrollDate": student['enrollDate'],
                    "endDate": student['expireDate'],
                    "totalHours": student['totalClasses'],
                    "consumedHours": student['consumedClasses'],
                    "remainingHours": student['remainingClasses'],
                    "status": student['status']
                }
            ]
        }
        
        print("API参数:")
        print(json.dumps(api_payload, ensure_ascii=False, indent=2))
        print("\n" + "-"*80 + "\n")
    
    # 生成统计信息
    print("=== 统计信息 ===")
    course_stats = {}
    coach_stats = {}
    
    for student in students_data:
        course_name = student['courseName']
        coach_name = student['coachName']
        
        course_stats[course_name] = course_stats.get(course_name, 0) + 1
        coach_stats[coach_name] = coach_stats.get(coach_name, 0) + 1
    
    print("课程分布:")
    for course, count in course_stats.items():
        print(f"  {course}: {count} 人")
    
    print("\n教练分布:")
    for coach, count in coach_stats.items():
        print(f"  {coach}: {count} 人")
    
    print(f"\n总计: {len(students_data)} 个学员")
    print(f"解析失败: {len(failed_files)} 个文件")
    
    if len(students_data) > preview_count:
        print(f"\n注意: 只显示了前{preview_count}个学员，还有{len(students_data) - preview_count}个学员未显示")

if __name__ == "__main__":
    main()
