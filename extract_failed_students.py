#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从失败的Excel文件名中提取学员姓名，并生成对应关系
"""

import os
import re
from datetime import datetime

def extract_student_names_from_filename(filename):
    """
    从文件名中提取学员姓名
    """
    # 移除文件扩展名
    name_part = filename.replace('.xls', '').replace('.xlsx', '').strip()
    
    # 常见的分隔符
    separators = ['、', ' ', '-', '_', '和', '与', '及']
    
    # 尝试分割姓名
    names = [name_part]  # 默认整个文件名就是一个姓名
    
    for sep in separators:
        if sep in name_part:
            split_names = [n.strip() for n in name_part.split(sep) if n.strip()]
            if len(split_names) > 1:
                names = split_names
                break
    
    # 过滤掉可能不是姓名的部分
    filtered_names = []
    for name in names:
        # 移除空格和特殊字符
        clean_name = re.sub(r'[^\u4e00-\u9fff\w]', '', name)
        # 中文姓名通常2-4个字符
        if 2 <= len(clean_name) <= 6 and clean_name:
            filtered_names.append(clean_name)
    
    return filtered_names if filtered_names else [name_part]

def generate_failed_students_mapping():
    """
    生成失败学员的映射关系
    """
    # 失败的文件列表（从分析结果中获取）
    failed_files = [
        "马溯言曹沐清.xls",
        "阮逸轩.xls", 
        "李睿轩.xls",
        "杨梓汐杨梓钰 .xls",
        "罗奕琪 .xls",
        "杨长歌封硕.xls",
        "梅子之.xls",
        "刘畅.xls",
        "李悦萌.xls",
        "杨涵帆.xls",
        "周志伟.xls",
        "洪颖莎.xls",
        "马小茜.xls",
        "杨晏清.xls",
        "聂荣圻.xls",
        "贾紫翘.xls",
        "赵晟麟.xls",
        "孙君泽.xls",
        "孟照心孟镜明.xls",
        "陈思和.xls",
        "赵家瑜.xls",
        "蔺科匀.xls",
        "程天一.xls",
        "刘思佳-杨紫若.xls",
        "张艺潆 .xls"
    ]
    
    print("=== 从失败文件名中提取学员姓名 ===\n")
    
    extracted_students = []
    
    for filename in failed_files:
        names = extract_student_names_from_filename(filename)
        print(f"文件: {filename}")
        print(f"提取的姓名: {', '.join(names)}")
        
        for name in names:
            extracted_students.append({
                "filename": filename,
                "student_name": name,
                "gender": "MALE",  # 默认性别，需要手动调整
                "age": 10,  # 默认年龄
                "phone": "13800000000",  # 默认手机号
                "courseId": 1,  # 默认课程ID，需要根据教练调整
                "courseName": "杨教练一对一",  # 默认课程名称
                "coachId": 1000,  # 默认教练ID，需要手动调整
                "coachName": "杨大冬",  # 默认教练姓名
                "enrollDate": "2024-01-01",  # 默认入学日期
                "status": "STUDYING"
            })
        print()
    
    print(f"总计提取到 {len(extracted_students)} 个学员姓名\n")
    
    # 生成API参数
    print("=== 生成失败学员的API创建参数 ===\n")
    
    api_params_lines = []
    api_params_lines.append("=== 失败文件中提取的学员API创建参数 ===")
    api_params_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    api_params_lines.append(f"总计: {len(extracted_students)} 个学员")
    api_params_lines.append("")
    api_params_lines.append("注意: 以下参数使用默认值，需要根据实际情况调整：")
    api_params_lines.append("- 性别默认为 MALE")
    api_params_lines.append("- 年龄默认为 10")
    api_params_lines.append("- 手机号默认为 13800000000")
    api_params_lines.append("- 课程和教练需要根据实际情况调整")
    api_params_lines.append("- 入学日期默认为 2024-01-01")
    api_params_lines.append("")
    api_params_lines.append("="*80)
    api_params_lines.append("")
    
    for i, student in enumerate(extracted_students, 1):
        api_params_lines.append(f"// 学员 {i}: {student['student_name']}")
        api_params_lines.append(f"// 来源文件: {student['filename']}")
        api_params_lines.append(f"// 课程: {student['courseName']} (ID: {student['courseId']})")
        api_params_lines.append(f"// 教练: {student['coachName']} (ID: {student['coachId']})")
        
        api_payload = {
            "studentInfo": {
                "name": student['student_name'],
                "gender": student['gender'],
                "age": student['age'],
                "phone": student['phone'],
                "email": "",
                "address": "",
                "parentName": "",
                "parentPhone": "",
                "campusId": 1
            },
            "courseInfoList": [
                {
                    "courseId": student['courseId'],
                    "courseName": student['courseName'],
                    "coachId": student['coachId'],
                    "coachName": student['coachName'],
                    "enrollDate": student['enrollDate'],
                    "endDate": "2025-12-31",
                    "totalHours": 20,  # 默认总课时
                    "consumedHours": 0,  # 默认已消耗课时
                    "remainingHours": 20,  # 默认剩余课时
                    "status": student['status']
                }
            ]
        }
        
        import json
        api_json = json.dumps(api_payload, ensure_ascii=False, indent=2)
        api_params_lines.append(api_json)
        api_params_lines.append("")
        api_params_lines.append("="*80)
        api_params_lines.append("")
    
    # 保存到文件
    output_file = "failed_students_api_parameters.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(api_params_lines))
        print(f"✓ 失败学员API参数已保存到: {output_file}")
    except Exception as e:
        print(f"✗ 保存文件失败: {e}")
    
    # 生成学员姓名列表
    print(f"\n=== 提取的学员姓名列表 ===")
    unique_names = list(set([s['student_name'] for s in extracted_students]))
    unique_names.sort()
    
    for i, name in enumerate(unique_names, 1):
        print(f"{i:2d}. {name}")
    
    print(f"\n总计: {len(unique_names)} 个不重复的学员姓名")
    
    # 生成需要手动调整的提醒
    print(f"\n=== 需要手动调整的信息 ===")
    print("1. 学员性别：根据姓名判断性别")
    print("2. 学员年龄：根据实际情况调整")
    print("3. 联系电话：需要获取真实的联系方式")
    print("4. 课程和教练：根据实际的课程安排调整courseId和coachId")
    print("5. 入学日期：根据实际的入学时间调整")
    print("6. 课时信息：根据实际的课时安排调整totalHours等")
    
    return extracted_students

if __name__ == "__main__":
    generate_failed_students_mapping()
