#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析失败的Excel文件
"""

import pandas as pd
import os
import glob
from datetime import datetime

def analyze_excel_file(file_path):
    """
    分析单个Excel文件的失败原因
    """
    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)
    
    analysis = {
        "file_name": file_name,
        "file_size": file_size,
        "error_type": "未知错误",
        "error_message": "",
        "possible_solution": "",
        "student_names": []
    }
    
    # 尝试不同的方法读取文件
    engines = ['xlrd', 'openpyxl']
    
    for engine in engines:
        try:
            df = pd.read_excel(file_path, engine=engine)
            if not df.empty:
                analysis["error_type"] = "读取成功"
                analysis["error_message"] = f"使用 {engine} 引擎成功读取"
                
                # 尝试提取学员姓名
                if '姓名' in df.columns:
                    names = df['姓名'].dropna().unique().tolist()
                    analysis["student_names"] = names
                elif 'name' in df.columns:
                    names = df['name'].dropna().unique().tolist()
                    analysis["student_names"] = names
                else:
                    # 尝试从第一列获取姓名
                    first_col = df.iloc[:, 0] if len(df.columns) > 0 else pd.Series()
                    if not first_col.empty:
                        potential_names = first_col.dropna().unique().tolist()[:5]  # 取前5个可能的姓名
                        analysis["student_names"] = potential_names
                
                return analysis
                
        except Exception as e:
            error_msg = str(e)
            analysis["error_message"] = error_msg
            
            # 分析错误类型
            if "Workbook corruption" in error_msg:
                analysis["error_type"] = "文件损坏"
                analysis["possible_solution"] = "文件可能损坏，建议用Excel重新保存或转换格式"
            elif "No module named" in error_msg:
                analysis["error_type"] = "缺少依赖"
                analysis["possible_solution"] = "安装相应的Python库"
            elif "Permission denied" in error_msg:
                analysis["error_type"] = "权限问题"
                analysis["possible_solution"] = "检查文件权限或关闭正在使用该文件的程序"
            elif "not supported" in error_msg.lower():
                analysis["error_type"] = "格式不支持"
                analysis["possible_solution"] = "文件格式可能不是标准Excel格式，建议重新保存"
            else:
                analysis["error_type"] = "读取错误"
                analysis["possible_solution"] = "尝试用Excel打开并重新保存文件"
    
    # 如果文件很小，可能是空文件或格式问题
    if file_size < 1024:  # 小于1KB
        analysis["error_type"] = "文件过小"
        analysis["possible_solution"] = "文件可能为空或格式错误"
    
    return analysis

def main():
    """
    主函数：分析所有失败的Excel文件
    """
    print("开始分析失败的Excel文件...")
    
    # 获取所有Excel文件
    files = glob.glob('课时记/*.xls')
    print(f"找到 {len(files)} 个Excel文件")
    
    failed_analyses = []
    success_count = 0
    
    for i, file_path in enumerate(files):
        print(f"\n分析文件 {i+1}/{len(files)}: {os.path.basename(file_path)}")
        
        analysis = analyze_excel_file(file_path)
        
        if analysis["error_type"] == "读取成功":
            success_count += 1
            print(f"✓ 读取成功")
            if analysis["student_names"]:
                print(f"  学员姓名: {', '.join(analysis['student_names'])}")
        else:
            failed_analyses.append(analysis)
            print(f"✗ {analysis['error_type']}: {analysis['error_message']}")
            if analysis["possible_solution"]:
                print(f"  建议: {analysis['possible_solution']}")
    
    print(f"\n=== 分析完成 ===")
    print(f"成功读取: {success_count} 个文件")
    print(f"读取失败: {len(failed_analyses)} 个文件")
    
    # 生成失败分析报告
    if failed_analyses:
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("Excel文件失败分析报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"总文件数: {len(files)}")
        report_lines.append(f"成功读取: {success_count}")
        report_lines.append(f"读取失败: {len(failed_analyses)}")
        report_lines.append("")
        
        # 按错误类型分组
        error_types = {}
        for analysis in failed_analyses:
            error_type = analysis["error_type"]
            if error_type not in error_types:
                error_types[error_type] = []
            error_types[error_type].append(analysis)
        
        report_lines.append("失败类型统计:")
        for error_type, analyses in error_types.items():
            report_lines.append(f"  {error_type}: {len(analyses)} 个文件")
        report_lines.append("")
        
        # 详细失败信息
        report_lines.append("详细失败信息:")
        report_lines.append("-" * 80)
        
        for i, analysis in enumerate(failed_analyses, 1):
            report_lines.append(f"{i}. 文件名: {analysis['file_name']}")
            report_lines.append(f"   文件大小: {analysis['file_size']} 字节")
            report_lines.append(f"   错误类型: {analysis['error_type']}")
            report_lines.append(f"   错误信息: {analysis['error_message']}")
            if analysis["possible_solution"]:
                report_lines.append(f"   解决建议: {analysis['possible_solution']}")
            if analysis["student_names"]:
                report_lines.append(f"   可能的学员姓名: {', '.join(analysis['student_names'])}")
            report_lines.append("")
        
        # 保存报告到文件
        report_file = "failed_files_analysis.txt"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))
            print(f"\n✓ 失败分析报告已保存到: {report_file}")
        except Exception as e:
            print(f"\n✗ 保存报告失败: {e}")
        
        # 在控制台显示摘要
        print(f"\n=== 失败文件摘要 ===")
        for error_type, analyses in error_types.items():
            print(f"\n{error_type} ({len(analyses)} 个文件):")
            for analysis in analyses:
                print(f"  - {analysis['file_name']}")
                if analysis["student_names"]:
                    print(f"    可能的学员: {', '.join(analysis['student_names'])}")

if __name__ == "__main__":
    main()
