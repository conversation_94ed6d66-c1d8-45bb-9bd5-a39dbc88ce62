import { useState, useEffect } from 'react';
import { StudentSearchParams, StudentUISearchParams } from '@/api/student/types';
import { Student } from '@/pages/student/types/student'; // 使用前端Student类型
import { API } from '@/api';
import { message } from 'antd';

// 记录正在进行的请求ID，防止重复调用
let pendingRequestId: string | null = null;

export const useStudentData = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [total, setTotal] = useState(0);
  // 使用简单的loading状态，与教练管理页面保持一致
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取学员列表
  const fetchStudents = async (params?: StudentSearchParams) => {
    // 生成唯一请求ID，用于日志跟踪
    const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    
    // 检查是否已有正在进行的请求
    if (pendingRequestId) {
      console.warn(`[fetchStudents ${requestId}] 已有请求正在进行，ID=${pendingRequestId}，忽略当前请求`);
      return [];
    }
    
    // 记录当前请求ID
    pendingRequestId = requestId;
    console.log(`[fetchStudents ${requestId}] 开始获取学员列表`, params);
    
    try {
      // 设置加载状态为true
      setLoading(true);
      console.log(`[fetchStudents ${requestId}] 设置loading=true`);

      // 确保有校区ID
      const currentCampusId = localStorage.getItem('currentCampusId');
      if (!currentCampusId) {
        console.warn(`[fetchStudents ${requestId}] 未选择校区，无法获取学员列表`);
        message.warning('请先选择校区');
        setLoading(false);
        pendingRequestId = null; // 清除请求ID
        return [];
      }

      // 添加默认的倒序排序参数
      const defaultParams: StudentSearchParams = {
        ...params,
        // 强制按ID倒序排序，确保新增学员总是显示在最前面
        sortField: 'id',
        sortOrder: 'desc',
        // 添加校区ID
        campusId: Number(currentCampusId)
      };

      console.log(`[fetchStudents ${requestId}] 准备调用API，参数:`, defaultParams);
      // 调用API获取学员列表
      const response = await API.student.getList(defaultParams);
      console.log(`[fetchStudents ${requestId}] API调用成功，获取到 ${response?.list?.length || 0} 条记录`);

      if (response && response.list) {
        // 更新状态
        setStudents(response.list);
        setFilteredStudents(response.list);
        setTotal(response.total);
        console.log(`[fetchStudents ${requestId}] 状态更新成功，总数: ${response.total}`);
      } else {
        console.error(`[fetchStudents ${requestId}] 学员列表响应格式不正确:`, response);
        setStudents([]);
        setFilteredStudents([]);
        setTotal(0);
      }

      return response?.list || [];
    } catch (error) {
      console.error(`[fetchStudents ${requestId}] 获取学员列表失败:`, error);
      message.error('获取学员列表失败');

      // 重置状态
      setStudents([]);
      setFilteredStudents([]);
      setTotal(0);

      return [];
    } finally {
      // 无论成功还是失败，都关闭加载状态
      console.log(`[fetchStudents ${requestId}] 完成，设置loading=false，清除请求ID`);
      setLoading(false);
      pendingRequestId = null; // 清除请求ID，允许下一个请求
    }
  };

  // 不在这里自动获取学员列表，而是由组件调用

  // 添加学员
  const addStudent = async (student: Omit<Student, 'id'>) => {
    try {
      setLoading(true);
      const newStudent = await API.student.add(student);

      // 重新获取学员列表，确保数据最新
      await fetchStudents({
        pageNum: currentPage,
        pageSize: pageSize
      });

      message.success('学员添加成功');
      return newStudent;
    } catch (error) {
      console.error('添加学员失败:', error);
      message.error('添加学员失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 更新学员
  const updateStudent = async (id: string, updatedData: Partial<Student> | any) => {
    try {
      setLoading(true);
      console.log('更新学员参数:', id, updatedData);

      // 检查是否是包含 courseInfoList 的新版更新请求
      if (updatedData && 
          updatedData.studentId !== undefined && 
          updatedData.studentInfo && 
          updatedData.courseInfoList && 
          Array.isArray(updatedData.courseInfoList)) {
        // 使用 updateWithCourse 方法
        console.log('使用 updateWithCourse 方法更新学员及课程:', updatedData);
        await API.student.updateWithCourse(updatedData);
        
        // 更新成功后，直接更新本地状态
        console.log('学员更新成功，更新本地状态');
        updateStudentLocally(Number(id), updatedData);
      } else {
        // 使用原来的 update 方法
        console.log('使用原来的 update 方法更新学员:', updatedData);
        // 确保ID不为空
        if (!id) {
          id = String(updatedData.id || updatedData.studentId);
          if (!id) {
            console.error('更新学员失败: 无法获取学员ID', updatedData);
            message.error('更新学员失败: 无法获取学员ID');
            setLoading(false);
            return;
          }
        }
        await API.student.update(id, updatedData);
        
        // 更新成功后，直接更新本地状态
        console.log('学员更新成功，更新本地状态');
        updateStudentLocally(Number(id), updatedData);
      }

      // 统一：更新成功后刷新列表（异步触发，不阻塞模态框关闭）
      fetchStudents({
        pageNum: currentPage,
        pageSize: pageSize
      });
      // 统一：广播刷新摘要事件
      try { window.dispatchEvent(new Event('student:list-summary:refresh')); } catch {}
    } catch (error) {
      console.error('更新学员失败:', error);
      message.error('更新学员失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 删除学员
  const deleteStudent = async (id: string) => {
    try {
      setLoading(true);
      await API.student.delete(id);

      // 重新获取学员列表，确保数据最新
      await fetchStudents({
        pageNum: currentPage,
        pageSize: pageSize
      });

      // 广播刷新摘要事件
      try { window.dispatchEvent(new Event('student:list-summary:refresh')); } catch {}

      message.success('学员已删除');
    } catch (error) {
      console.error('删除学员失败:', error);
      message.error('删除学员失败');
    } finally {
      setLoading(false);
    }
  };

  // 将前端搜索参数转换为API搜索参数
  const convertToApiSearchParams = (uiParams: StudentUISearchParams): StudentSearchParams => {
    const apiParams: StudentSearchParams = {
      pageNum: currentPage,
      pageSize: pageSize,
      // 默认按创建时间倒序排序
      sortField: 'createdTime',
      sortOrder: 'desc'
    };

    // 处理搜索文本
    if (uiParams.searchText) {
      apiParams.keyword = uiParams.searchText;
    }

    // 处理状态
    if (uiParams.selectedStatus) {
      // 根据状态值进行映射
      const statusMap: Record<string, 'normal' | 'expired' | 'graduated' | 'STUDYING'> = {
        'active': 'normal',
        'ACTIVE': 'normal',
        'inactive': 'expired',
        'INACTIVE': 'expired',
        'pending': 'graduated',
        'PENDING': 'graduated',
        'STUDYING': 'STUDYING',
        'normal': 'normal',
        'expired': 'expired',
        'graduated': 'graduated'
      };

      apiParams.status = statusMap[uiParams.selectedStatus] || 'normal';
    }

    // 处理课程
    if (uiParams.selectedCourse) {
      apiParams.courseId = uiParams.selectedCourse;
    }

    // 处理报名月份
    if (uiParams.enrollMonth) {
      const year = uiParams.enrollMonth.year();
      const month = uiParams.enrollMonth.month() + 1;
      const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      const endDate = uiParams.enrollMonth.endOf('month').format('YYYY-MM-DD');

      apiParams.enrollDateStart = startDate;
      apiParams.enrollDateEnd = endDate;
    }

    // 处理排序
    if (uiParams.sortOrder) {
      switch(uiParams.sortOrder) {
        case 'enrollDateAsc':
          apiParams.sortField = 'enrollDate';
          apiParams.sortOrder = 'asc';
          break;
        case 'enrollDateDesc':
          apiParams.sortField = 'enrollDate';
          apiParams.sortOrder = 'desc';
          break;
        case 'ageAsc':
          apiParams.sortField = 'age';
          apiParams.sortOrder = 'asc';
          break;
        case 'ageDesc':
          apiParams.sortField = 'age';
          apiParams.sortOrder = 'desc';
          break;
        case 'remainingClassesAsc':
          apiParams.sortField = 'remainingClasses';
          apiParams.sortOrder = 'asc';
          break;
        case 'remainingClassesDesc':
          apiParams.sortField = 'remainingClasses';
          apiParams.sortOrder = 'desc';
          break;
        case 'lastClassDateAsc':
          apiParams.sortField = 'lastClassDate';
          apiParams.sortOrder = 'asc';
          break;
        case 'lastClassDateDesc':
          apiParams.sortField = 'lastClassDate';
          apiParams.sortOrder = 'desc';
          break;
      }
    }

    return apiParams;
  };

  // 过滤学员数据
  const filterStudents = async (uiParams: StudentUISearchParams) => {
    try {
      // 设置加载状态为true
      setLoading(true);

      // 确保有校区ID
      const currentCampusId = localStorage.getItem('currentCampusId');
      if (!currentCampusId) {
        console.warn('未选择校区，无法过滤学员数据');
        message.warning('请先选择校区');
        setLoading(false);
        return [];
      }

      // 将UI搜索参数转换为API搜索参数
      const apiParams = convertToApiSearchParams(uiParams);
      // 添加校区ID
      apiParams.campusId = Number(currentCampusId);

      // 调用API获取过滤后的学员列表
      const response = await API.student.getList(apiParams);

      if (response && response.list) {
        // 更新状态
        setFilteredStudents(response.list);
        setTotal(response.total);
      } else {
        console.error('过滤学员数据响应格式不正确:', response);
        setFilteredStudents([]);
        setTotal(0);
      }

      return response?.list || [];
    } catch (error) {
      console.error('过滤学员数据失败:', error);
      message.error('过滤学员数据失败');

      // 重置状态
      setFilteredStudents([]);
      setTotal(0);

      return [];
    } finally {
      // 无论成功还是失败，都关闭加载状态
      setLoading(false);
    }
  };

  // 重置数据
  const resetData = async () => {
    setCurrentPage(1);

    try {
      await fetchStudents({
        pageNum: 1,
        pageSize: pageSize
      });
    } catch (error) {
      console.error('重置数据失败:', error);
      message.error('重置数据失败');
    }
  };

  // 处理分页变化
  const handlePageChange = async (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);

    // 使用fetchStudents获取数据，让重复请求检测机制生效
    return fetchStudents({
      pageNum: page,
      pageSize: size || pageSize
    });
  };

  // 直接将新创建的学员添加到列表开头
  const addNewStudentToList = (newStudent: Student) => {
    try {
      console.log('添加新学员到列表:', newStudent);

      // 确保学生对象有效性
      if (!newStudent || typeof newStudent !== 'object') {
        console.error('无效的学员数据:', newStudent);
        return;
      }

      // 确保必要字段存在
      if (!newStudent.id) {
        console.error('新学员缺少ID字段:', newStudent);
        return;
      }

      // 检查学员是否已存在于列表中
      const exists = students.some(s => 
        s.id === newStudent.id || 
        (s.studentId && s.studentId === newStudent.studentId)
      );

      if (exists) {
        console.log('该学员已存在于列表中，不重复添加:', newStudent.id);
        return;
      }

      // 无论当前在哪一页，都将新学员添加到列表开头
      setStudents(prevStudents => [newStudent, ...prevStudents]);
      setFilteredStudents(prevStudents => [newStudent, ...prevStudents]);
      // 更新总数
      setTotal(prevTotal => prevTotal + 1);

      // 如果当前不在第一页，则自动跳转到第一页
      if (currentPage !== 1) {
        console.log('添加新学员后自动跳转到第一页');
        setCurrentPage(1);
      }

      console.log('新学员成功添加到列表:', newStudent.name);
    } catch (error) {
      console.error('添加学员到列表失败:', error);
      message.error('添加学员到列表失败');
    }
  };

  // 新增：本地更新学员打卡后的课时信息
  const updateStudentAttendanceLocally = (studentId: number, courseId: number, consumedDuration: number) => {
    console.log(`[updateLocally]尝试更新：学员ID=${studentId}, 课程ID=${courseId}, 消耗时长=${consumedDuration}`);

    const updateState = (prevState: Student[]) => {
      let stateChanged = false;
      const newState = prevState.map(student => {
        // ★ 修改 ID 比较逻辑：优先用 studentId (number)，否则用 id (string 转 number)
        const studentMatches = (student.studentId !== undefined && student.studentId === studentId) || 
                             (student.studentId === undefined && student.id !== undefined && Number(student.id) === studentId);

        if (studentMatches) { 
          let studentCoursesChanged = false;
          const originalCourses = student.courses ?? [];
          const updatedCourses = originalCourses.map(course => {
            // ★ 转换为数字进行比较
            if (Number(course.courseId) === courseId) { 
              const currentRemaining = course.remainingHours ?? 0;
              const newRemaining = Math.max(0, currentRemaining - consumedDuration);
              if (newRemaining !== currentRemaining) { 
                console.log(`[updateLocally] 找到课程并更新: ${course.courseName} (ID: ${course.courseId}), 原剩余: ${currentRemaining}, 新剩余: ${newRemaining}`);
                studentCoursesChanged = true; 
                return {
                  ...course,
                  remainingHours: newRemaining,
                };
              } else {
                 console.log(`[updateLocally] 找到课程但课时未变: ${course.courseName}`);
              }
            }
            return course; 
          });

          if (studentCoursesChanged) {
            console.log(`[updateLocally] 找到学员并更新其 courses: ${student.name} (ID: ${student.studentId})`);
            stateChanged = true;
            return {
              ...student, 
              courses: updatedCourses, 
            };
          } else {
             console.log(`[updateLocally] 找到学员但其课程未更新: ${student.name}`);
          }
        }
        return student; 
      });

      // ★ 修改这里的逻辑：即使 stateChanged 为 false (因为ID不匹配或课时未变)，
      // 但既然 API 调用成功且 duration > 0，我们仍然应该返回 newState 以强制更新引用。
      // 只有在 consumedDuration 为 0 时，才考虑返回 prevState。
      if (consumedDuration !== 0) {
          if (!stateChanged) {
              console.warn(`[updateLocally] ID匹配失败或课时未变，但强制更新引用，因为 duration=${consumedDuration}`);
          }
          console.log(`[updateLocally] 返回新数组引用 (强制或因改变)`);
          return newState; // 强制返回新数组
      } else {
          // 如果消耗课时为0，则按原来的逻辑判断是否返回新引用
          if (stateChanged) {
            console.log(`[updateLocally] 状态已改变 (duration=0)，返回新数组引用`);
            return newState;
          }
          console.log(`[updateLocally] 状态未改变 (duration=0)，返回原数组引用`);
          return prevState; 
      }
    };

    // 更新状态，React 会比较新旧状态引用，如果不同则触发重渲染
    setStudents(updateState);
    setFilteredStudents(updateState);

    console.log(`[updateLocally] 更新调用完成`);
  };

  // 新增：本地更新学员信息
  const updateStudentLocally = (studentId: number, updatedData: Partial<Student>) => {
    console.log(`[updateLocally] 尝试更新本地学员: ID=${studentId}, 更新数据:`, updatedData);
    setStudents(prevStudents => {
      const updated = prevStudents.map(student => {
        if (String(student.id) === String(studentId) || 
            (student.studentId !== undefined && student.studentId === studentId)) {
          return {
            ...student,
            ...updatedData,
          };
        }
        return student;
      });
      console.log(`[updateLocally] 本地状态已更新，新数组引用`);
      return updated;
    });
    setFilteredStudents(prevStudents => {
      const updated = prevStudents.map(student => {
        if (String(student.id) === String(studentId) || 
            (student.studentId !== undefined && student.studentId === studentId)) {
          return {
            ...student,
            ...updatedData,
          };
        }
        return student;
      });
      console.log(`[updateLocally] 本地过滤状态已更新，新数组引用`);
      return updated;
    });
    console.log(`[updateLocally] 本地更新调用完成`);
  };

  return {
    students: filteredStudents,
    totalStudents: total,
    loading,
    currentPage,
    pageSize,
    addStudent,
    updateStudent,
    deleteStudent,
    filterStudents,
    resetData,
    handlePageChange,
    fetchStudents,
    addNewStudentToList,
    updateStudentAttendanceLocally,
    updateStudentLocally
  };
};