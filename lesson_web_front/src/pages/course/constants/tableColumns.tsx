import React from 'react';
import { Space, Button, Tooltip, Tag } from 'antd';
import { EditOutlined, DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { Course, CourseStatus } from '../types/course';
import { AlignType } from 'rc-table/lib/interface';
import type { SortOrder } from 'antd/es/table/interface';

// 获取教练名称
const getCoachNames = (coaches?: { id: number; name: string }[]) => {
  if (!coaches || coaches.length === 0) return '';
  return coaches.map(coach => coach.name).join(', ');
};

// 课程类型颜色映射
const courseTypeColorMap: Record<string, string> = {
  '大课': 'orange',
  '一对一': 'geekblue',
  '小班': 'cyan',
  '体育类': 'blue',
  '艺术类': 'purple',
  '学术类': 'green',
  '英语': 'green',
  '数学': 'gold',
  '音乐': 'magenta',
  '绘画': 'volcano',
  '舞蹈': 'pink',
  '体育': 'blue'
};

// 获取课程分类名称
const getTypeName = (type: string) => {
  // 直接返回类型名称，因为API返回的已经是可读的名称
  return type || '未知类型';
};

// 渲染课程类型标签
const renderCourseTypeTag = (type: string) => {
  const color = courseTypeColorMap[type] || 'default';
  return (
    <Tag color={color} style={{ minWidth: '65px', textAlign: 'center' }}>
      {type || '未知类型'}
    </Tag>
  );
};

// 渲染状态标签
export const renderStatusTag = (status: string) => {
  // 统一处理状态值
  let color = '';
  let text = '';

  // 转换为大写字符串以便比较
  const normalizedStatus = status?.toString().toUpperCase() || '';
  console.log('表格渲染状态标签, 原始状态:', status, '标准化后:', normalizedStatus);

  if (normalizedStatus === CourseStatus.PUBLISHED || normalizedStatus === 'PUBLISHED') {
    color = 'green';
    text = '已发布';
  } else if (normalizedStatus === CourseStatus.SUSPENDED) {
    color = 'orange';
    text = '已暂停';
  } else if (normalizedStatus === CourseStatus.TERMINATED) {
    color = 'red';
    text = '已终止';
  } else if (normalizedStatus === CourseStatus.DRAFT || normalizedStatus === 'DRAFT') {
    color = 'default';
    text = '草稿';
  } else {
    // 未知状态
    color = 'default';
    text = status || '未知状态';
  }

  console.log('渲染状态标签结果:', { color, text });

  return <Tag color={color} style={{ display: 'inline-flex', alignItems: 'center', height: '22px', padding: '0 8px', fontSize: '12px', lineHeight: '22px' }}>{text}</Tag>;
};

export const getTableColumns = (
  onEdit: (record: Course) => void,
  onShowDetail: (record: Course) => void,
  onDelete: (id: string, name: string) => void
) => [
  {
    title: '课程名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center' as AlignType,
  },
  {
    title: '课程类型',
    dataIndex: 'type',
    key: 'type',
    align: 'center' as AlignType,
    render: (type: string) => renderCourseTypeTag(type),
  },
  {
    title: '总课时',
    dataIndex: 'totalHours',
    key: 'totalHours',
    align: 'center' as AlignType,
    render: (hours: number) => `${hours}小时`,
    sorter: true,
  },
  {
    title: '已销课时',
    dataIndex: 'consumedHours',
    key: 'consumedHours',
    align: 'center' as AlignType,
    render: (hours: number) => `${hours}小时`,
    sorter: true,
  },
  {
    title: '上课教练',
    dataIndex: 'coaches',
    key: 'coaches',
    align: 'center' as AlignType,
    render: (coaches: { id: number; name: string }[]) => getCoachNames(coaches),
  },
  {
    title: '课程单价',
    dataIndex: 'price',
    key: 'price',
    align: 'center' as AlignType,
    render: (price: number) => `¥${price}`,
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center' as AlignType,
    render: (status: string) => renderStatusTag(status),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    align: 'center' as AlignType,
    render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center' as AlignType,
    render: (_: any, record: Course) => (
      <Space size="middle">
        <Tooltip title="编辑">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              console.log('点击编辑按钮，课程数据:', JSON.stringify(record, null, 2));
              // 直接传递原始数据，不做任何修改
              onEdit(record);
            }}
          />
        </Tooltip>
        <Tooltip title="详情">
          <Button
            type="text"
            size="small"
            icon={<InfoCircleOutlined />}
            onClick={() => onShowDetail(record)}
          />
        </Tooltip>
        <Tooltip title="删除">
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDelete(record.id, record.name)}
          />
        </Tooltip>
      </Space>
    ),
  },
];