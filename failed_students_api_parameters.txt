=== 失败文件中提取的学员API创建参数 ===
生成时间: 2025-08-11 17:08:08
总计: 26 个学员

注意: 以下参数使用默认值，需要根据实际情况调整：
- 性别默认为 MALE
- 年龄默认为 10
- 手机号默认为 13800000000
- 课程和教练需要根据实际情况调整
- 入学日期默认为 2024-01-01

================================================================================

// 学员 1: 马溯言曹沐清
// 来源文件: 马溯言曹沐清.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "马溯言曹沐清",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 2: 阮逸轩
// 来源文件: 阮逸轩.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "阮逸轩",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 3: 李睿轩
// 来源文件: 李睿轩.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "李睿轩",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 4: 杨梓汐杨梓钰
// 来源文件: 杨梓汐杨梓钰 .xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "杨梓汐杨梓钰",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 5: 罗奕琪
// 来源文件: 罗奕琪 .xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "罗奕琪",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 6: 杨长歌封硕
// 来源文件: 杨长歌封硕.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "杨长歌封硕",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 7: 梅子之
// 来源文件: 梅子之.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "梅子之",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 8: 刘畅
// 来源文件: 刘畅.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "刘畅",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 9: 李悦萌
// 来源文件: 李悦萌.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "李悦萌",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 10: 杨涵帆
// 来源文件: 杨涵帆.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "杨涵帆",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 11: 周志伟
// 来源文件: 周志伟.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "周志伟",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 12: 洪颖莎
// 来源文件: 洪颖莎.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "洪颖莎",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 13: 马小茜
// 来源文件: 马小茜.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "马小茜",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 14: 杨晏清
// 来源文件: 杨晏清.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "杨晏清",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 15: 聂荣圻
// 来源文件: 聂荣圻.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "聂荣圻",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 16: 贾紫翘
// 来源文件: 贾紫翘.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "贾紫翘",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 17: 赵晟麟
// 来源文件: 赵晟麟.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "赵晟麟",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 18: 孙君泽
// 来源文件: 孙君泽.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "孙君泽",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 19: 孟照心孟镜明
// 来源文件: 孟照心孟镜明.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "孟照心孟镜明",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 20: 陈思和
// 来源文件: 陈思和.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "陈思和",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 21: 赵家瑜
// 来源文件: 赵家瑜.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "赵家瑜",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 22: 蔺科匀
// 来源文件: 蔺科匀.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "蔺科匀",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 23: 程天一
// 来源文件: 程天一.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "程天一",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 24: 刘思佳
// 来源文件: 刘思佳-杨紫若.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "刘思佳",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 25: 杨紫若
// 来源文件: 刘思佳-杨紫若.xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "杨紫若",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 26: 张艺潆
// 来源文件: 张艺潆 .xls
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
{
  "studentInfo": {
    "name": "张艺潆",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-01",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 0,
      "remainingHours": 20,
      "status": "STUDYING"
    }
  ]
}

================================================================================
