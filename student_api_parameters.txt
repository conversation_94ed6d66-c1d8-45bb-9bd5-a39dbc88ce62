=== 成功解析 30 个学员，生成API创建参数 ===

// 学员 1: 肖靖雯
// 课程: 肖教练一对一 (ID: 5)
// 教练: 上官 (ID: 1008)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "肖靖雯",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1008,
      "coachName": "上官",
      "enrollDate": "2025-02-28",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 2: 任俊宇
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 12 条
{
  "studentInfo": {
    "name": "任俊宇",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-05-08",
      "endDate": "2025-12-31",
      "totalHours": 22,
      "consumedHours": 12,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 3: 王均庭
// 课程: 武教练一对一 (ID: 2)
// 教练: 武文册 (ID: 1001)
// 课时记录: 8 条
{
  "studentInfo": {
    "name": "王均庭",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 2,
      "courseName": "武教练一对一",
      "coachId": 1001,
      "coachName": "武文册",
      "enrollDate": "2024-08-15",
      "endDate": "2025-12-31",
      "totalHours": 18,
      "consumedHours": 8,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 4: 常景程
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 8 条
{
  "studentInfo": {
    "name": "常景程",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-08-20",
      "endDate": "2025-12-31",
      "totalHours": 18,
      "consumedHours": 8,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 5: 朱景丽
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 9 条
{
  "studentInfo": {
    "name": "朱景丽",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-07-31",
      "endDate": "2025-12-31",
      "totalHours": 19,
      "consumedHours": 9,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 6: 周其姝
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 14 条
{
  "studentInfo": {
    "name": "周其姝",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2023-11-04",
      "endDate": "2025-12-31",
      "totalHours": 24,
      "consumedHours": 14,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 7: 徐璐
// 课程: 肖教练一对一 (ID: 5)
// 教练: 上官 (ID: 1008)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "徐璐",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1008,
      "coachName": "上官",
      "enrollDate": "2025-02-09",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 8: 康哲宇
// 课程: 武教练一对一 (ID: 2)
// 教练: 武文册 (ID: 1001)
// 课时记录: 8 条
{
  "studentInfo": {
    "name": "康哲宇",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 2,
      "courseName": "武教练一对一",
      "coachId": 1001,
      "coachName": "武文册",
      "enrollDate": "2024-07-08",
      "endDate": "2025-12-31",
      "totalHours": 18,
      "consumedHours": 8,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 9: 黎可欣
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "黎可欣",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-03-03",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 10: 张子茗张宛晴
// 课程: 杨教练一对一 (ID: 1)
// 教练: 苗寒青 (ID: 1002)
// 课时记录: 13 条
{
  "studentInfo": {
    "name": "张子茗张宛晴",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1002,
      "coachName": "苗寒青",
      "enrollDate": "2024-09-20",
      "endDate": "2025-12-31",
      "totalHours": 23,
      "consumedHours": 13,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 11: 彭博雅
// 课程: 肖教练一对一 (ID: 5)
// 教练: 上官 (ID: 1008)
// 课时记录: 13 条
{
  "studentInfo": {
    "name": "彭博雅",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1008,
      "coachName": "上官",
      "enrollDate": "2024-02-04",
      "endDate": "2025-12-31",
      "totalHours": 23,
      "consumedHours": 13,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 12: 马千淇
// 课程: 肖教练一对一 (ID: 5)
// 教练: 上官 (ID: 1008)
// 课时记录: 5 条
{
  "studentInfo": {
    "name": "马千淇",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1008,
      "coachName": "上官",
      "enrollDate": "2025-05-02",
      "endDate": "2025-12-31",
      "totalHours": 15,
      "consumedHours": 5,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 13: 杨思乔
// 课程: 大课 (ID: 4)
// 教练: 张鲁风 (ID: 1004)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "杨思乔",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 4,
      "courseName": "大课",
      "coachId": 1004,
      "coachName": "张鲁风",
      "enrollDate": "2023-12-06",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 14: 向志轩
// 课程: 武教练一对一 (ID: 2)
// 教练: 武文册 (ID: 1001)
// 课时记录: 8 条
{
  "studentInfo": {
    "name": "向志轩",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 2,
      "courseName": "武教练一对一",
      "coachId": 1001,
      "coachName": "武文册",
      "enrollDate": "2024-07-01",
      "endDate": "2025-12-31",
      "totalHours": 18,
      "consumedHours": 8,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 15: 赵莫野
// 课程: 杨教练一对一 (ID: 1)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 5 条
{
  "studentInfo": {
    "name": "赵莫野",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-02-25",
      "endDate": "2025-12-31",
      "totalHours": 15,
      "consumedHours": 5,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 16: 陈华彬
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 37 条
{
  "studentInfo": {
    "name": "陈华彬",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-02-01",
      "endDate": "2025-12-31",
      "totalHours": 47,
      "consumedHours": 37,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 17: 刘忻瑶
// 课程: 张鲁风一对一 (ID: 3)
// 教练: 张鲁风 (ID: 1004)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "刘忻瑶",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 3,
      "courseName": "张鲁风一对一",
      "coachId": 1004,
      "coachName": "张鲁风",
      "enrollDate": "2024-03-22",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 18: 刘熠辰
// 课程: 大课 (ID: 4)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 3 条
{
  "studentInfo": {
    "name": "刘熠辰",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 4,
      "courseName": "大课",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2023-12-30",
      "endDate": "2025-12-31",
      "totalHours": 13,
      "consumedHours": 3,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 19: 李迎迎
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 14 条
{
  "studentInfo": {
    "name": "李迎迎",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-03-07",
      "endDate": "2025-12-31",
      "totalHours": 24,
      "consumedHours": 14,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 20: 李旸煜馨
// 课程: 杨教练一对一 (ID: 1)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 5 条
{
  "studentInfo": {
    "name": "李旸煜馨",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-02-01",
      "endDate": "2025-12-31",
      "totalHours": 15,
      "consumedHours": 5,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 21: 孟琳曦
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 13 条
{
  "studentInfo": {
    "name": "孟琳曦",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-02-29",
      "endDate": "2025-12-31",
      "totalHours": 23,
      "consumedHours": 13,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 22: 孟琳曦关子瑄
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "孟琳曦关子瑄",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-01-22",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 23: 袁博
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 8 条
{
  "studentInfo": {
    "name": "袁博",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-07-05",
      "endDate": "2025-12-31",
      "totalHours": 18,
      "consumedHours": 8,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 24: 刘昕悦
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "刘昕悦",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2024-02-18",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 25: 林远图
// 课程: 杨教练一对一 (ID: 1)
// 教练: 杨大冬 (ID: 1000)
// 课时记录: 12 条
{
  "studentInfo": {
    "name": "林远图",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 1,
      "courseName": "杨教练一对一",
      "coachId": 1000,
      "coachName": "杨大冬",
      "enrollDate": "2023-11-05",
      "endDate": "2025-12-31",
      "totalHours": 22,
      "consumedHours": 12,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 26: 李伯温
// 课程: 大课 (ID: 4)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 8 条
{
  "studentInfo": {
    "name": "李伯温",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 4,
      "courseName": "大课",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2023-12-17",
      "endDate": "2025-12-31",
      "totalHours": 18,
      "consumedHours": 8,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 27: 混血
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 6 条
{
  "studentInfo": {
    "name": "混血",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-05-05",
      "endDate": "2025-12-31",
      "totalHours": 16,
      "consumedHours": 6,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 28: 崔允曦
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "崔允曦",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-01-28",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 29: 向嘉怡
// 课程: 肖教练一对一 (ID: 5)
// 教练: 肖凡楠 (ID: 1009)
// 课时记录: 10 条
{
  "studentInfo": {
    "name": "向嘉怡",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1009,
      "coachName": "肖凡楠",
      "enrollDate": "2024-01-19",
      "endDate": "2025-12-31",
      "totalHours": 20,
      "consumedHours": 10,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

// 学员 30: 关子瑄
// 课程: 肖教练一对一 (ID: 5)
// 教练: 上官 (ID: 1008)
// 课时记录: 9 条
{
  "studentInfo": {
    "name": "关子瑄",
    "gender": "MALE",
    "age": 10,
    "phone": "13800000000",
    "email": "",
    "address": "",
    "parentName": "",
    "parentPhone": "",
    "campusId": 1
  },
  "courseInfoList": [
    {
      "courseId": 5,
      "courseName": "肖教练一对一",
      "coachId": 1008,
      "coachName": "上官",
      "enrollDate": "2024-02-29",
      "endDate": "2025-12-31",
      "totalHours": 19,
      "consumedHours": 9,
      "remainingHours": 10,
      "status": "STUDYING"
    }
  ]
}

================================================================================

=== 统计信息 ===
课程分布:
  肖教练一对一: 12 人
  武教练一对一: 3 人
  杨教练一对一: 11 人
  大课: 3 人
  张鲁风一对一: 1 人

教练分布:
  上官: 5 人
  肖凡楠: 11 人
  武文册: 3 人
  杨大冬: 8 人
  苗寒青: 1 人
  张鲁风: 2 人

总计: 30 个学员
解析失败: 25 个文件