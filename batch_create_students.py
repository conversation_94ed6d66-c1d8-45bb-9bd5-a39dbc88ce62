#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量创建学员脚本
从课时记录Excel文件中提取学员信息，生成创建学员的API参数
"""

import pandas as pd
import os
import glob
import json
from datetime import datetime
import re

# 课程类型和教练映射
COURSE_MAPPING = {
    # 根据图片中的课程信息映射
    "杨教练一对一": {"id": 1, "name": "杨教练一对一"},
    "武教练一对一": {"id": 2, "name": "武教练一对一"},
    "张鲁风一对一": {"id": 3, "name": "张鲁风一对一"},
    "大课": {"id": 4, "name": "大课"},
    "肖教练一对一": {"id": 5, "name": "肖教练一对一"},
    "肖教练一对一2": {"id": 6, "name": "肖教练一对一"}  # 可能有重复的肖教练课程
}

COACH_MAPPING = {
    # 根据图片中的教练信息映射
    "杨大冬": {"id": 1000, "name": "杨大冬"},
    "武文册": {"id": 1001, "name": "武文册"},
    "苗寒青": {"id": 1002, "name": "苗寒青"},
    "张宇彪": {"id": 1003, "name": "张宇彪"},
    "张鲁风": {"id": 1004, "name": "张鲁风"},
    "test": {"id": 1007, "name": "test"},
    "上官": {"id": 1008, "name": "上官"},
    "肖凡楠": {"id": 1009, "name": "肖凡楠"},
    "测试": {"id": 1010, "name": "测试"}
}

def infer_course_id(course_type, coach_name):
    """
    根据课程类型和教练姓名推理课程ID
    """
    if course_type == "1对1":
        # 根据教练姓名推理具体的一对一课程
        if coach_name in ["杨大冬"]:
            return 1  # 杨教练一对一
        elif coach_name in ["武文册"]:
            return 2  # 武教练一对一
        elif coach_name in ["张鲁风"]:
            return 3  # 张鲁风一对一
        elif coach_name in ["肖凡楠", "上官"]:
            return 5  # 肖教练一对一
        else:
            # 默认返回第一个一对一课程
            return 1
    elif course_type == "大课":
        return 4  # 大课
    else:
        # 默认返回一对一课程
        return 1

def get_coach_id(coach_name):
    """
    根据教练姓名获取教练ID
    """
    if coach_name in COACH_MAPPING:
        return COACH_MAPPING[coach_name]["id"]
    else:
        print(f"警告: 未找到教练 '{coach_name}' 的映射，使用默认ID 1000")
        return 1000  # 默认教练ID

def parse_student_data(file_path):
    """
    解析单个学员的Excel文件
    """
    try:
        df = pd.read_excel(file_path, engine='xlrd')
        if df.empty:
            return None

        # 获取学员基本信息（从第一行记录）
        first_record = df.iloc[0]

        student_name = first_record.get('姓名', '').strip()
        course_type = first_record.get('课程名称', '').strip()
        coach_name = first_record.get('老师', '').strip()

        if not student_name:
            print(f"警告: 文件 {file_path} 中没有找到学员姓名")
            return None

        # 推理课程ID和教练ID
        course_id = infer_course_id(course_type, coach_name)
        coach_id = get_coach_id(coach_name)

        # 计算课时统计
        total_classes = len(df)
        consumed_classes = total_classes  # 假设所有记录都是已消耗的课时

        # 获取最早和最晚的上课日期
        dates = df['上课日期'].dropna().tolist()
        if dates:
            # 转换日期格式
            parsed_dates = []
            for date in dates:
                try:
                    if isinstance(date, str):
                        parsed_date = datetime.strptime(date, '%Y-%m-%d')
                    else:
                        parsed_date = pd.to_datetime(date)
                    parsed_dates.append(parsed_date)
                except:
                    continue

            if parsed_dates:
                enroll_date = min(parsed_dates).strftime('%Y-%m-%d')
                last_class_date = max(parsed_dates).strftime('%Y-%m-%d')
            else:
                enroll_date = '2024-01-01'
                last_class_date = '2024-12-31'
        else:
            enroll_date = '2024-01-01'
            last_class_date = '2024-12-31'

        # 获取正确的课程名称
        course_name_mapping = {
            1: "杨教练一对一",
            2: "武教练一对一",
            3: "张鲁风一对一",
            4: "大课",
            5: "肖教练一对一",
            6: "肖教练一对一"
        }

        # 构建学员数据
        student_data = {
            "name": student_name,
            "gender": "MALE",  # 默认性别，需要手动调整
            "age": 10,  # 默认年龄，需要手动调整
            "phone": "13800000000",  # 默认手机号，需要手动调整
            "email": "",
            "address": "",
            "parentName": "",
            "parentPhone": "",
            "courseId": course_id,
            "courseName": course_name_mapping.get(course_id, course_type),
            "coachId": coach_id,
            "coachName": coach_name,
            "enrollDate": enroll_date,
            "expireDate": "2025-12-31",  # 默认过期时间
            "totalClasses": total_classes + 10,  # 总课时 = 已消耗 + 剩余
            "consumedClasses": consumed_classes,
            "remainingClasses": 10,  # 默认剩余10课时
            "status": "STUDYING",
            "campusId": 1,  # 默认校区ID，需要根据实际情况调整
            "lastClassDate": last_class_date,
            "courseType": course_type,
            "classRecords": len(df)
        }

        return student_data

    except Exception as e:
        print(f"解析文件 {file_path} 失败: {e}")
        return None

def main():
    """
    主函数：批量解析所有学员文件并生成创建参数
    """
    print("开始批量解析学员课时记录...")

    # 获取所有Excel文件
    files = glob.glob('课时记/*.xls')
    print(f"找到 {len(files)} 个Excel文件")

    students_data = []
    failed_files = []

    for i, file_path in enumerate(files):
        print(f"\n处理文件 {i+1}/{len(files)}: {os.path.basename(file_path)}")

        student_data = parse_student_data(file_path)
        if student_data:
            students_data.append(student_data)
            print(f"✓ 成功解析学员: {student_data['name']}")
            print(f"  课程: {student_data['courseName']} (ID: {student_data['courseId']})")
            print(f"  教练: {student_data['coachName']} (ID: {student_data['coachId']})")
            print(f"  课时记录: {student_data['classRecords']} 条")
        else:
            failed_files.append(file_path)
            print(f"✗ 解析失败")

    print(f"\n=== 解析完成 ===")
    print(f"成功解析: {len(students_data)} 个学员")
    print(f"解析失败: {len(failed_files)} 个文件")

    if failed_files:
        print("\n失败的文件:")
        for file_path in failed_files:
            print(f"  - {os.path.basename(file_path)}")

    return students_data, failed_files

def generate_api_parameters(students_data):
    """
    生成API创建参数
    """
    print(f"\n=== 生成API创建参数 ===")

    for i, student in enumerate(students_data):
        print(f"\n--- 学员 {i+1}: {student['name']} ---")

        # 构建API调用参数
        api_payload = {
            "studentInfo": {
                "name": student['name'],
                "gender": student['gender'],
                "age": student['age'],
                "phone": student['phone'],
                "email": student['email'],
                "address": student['address'],
                "parentName": student['parentName'],
                "parentPhone": student['parentPhone'],
                "campusId": student['campusId']
            },
            "courseInfoList": [
                {
                    "courseId": student['courseId'],
                    "courseName": student['courseName'],
                    "coachId": student['coachId'],
                    "coachName": student['coachName'],
                    "enrollDate": student['enrollDate'],
                    "endDate": student['expireDate'],
                    "totalHours": student['totalClasses'],
                    "consumedHours": student['consumedClasses'],
                    "remainingHours": student['remainingClasses'],
                    "status": student['status']
                }
            ]
        }

        print("API调用参数:")
        print(json.dumps(api_payload, ensure_ascii=False, indent=2))

def generate_statistics(students_data):
    """
    生成统计信息
    """
    print(f"\n=== 统计信息 ===")
    course_stats = {}
    coach_stats = {}

    for student in students_data:
        course_name = student['courseName']
        coach_name = student['coachName']

        course_stats[course_name] = course_stats.get(course_name, 0) + 1
        coach_stats[coach_name] = coach_stats.get(coach_name, 0) + 1

    print("课程分布:")
    for course, count in course_stats.items():
        print(f"  {course}: {count} 人")

    print("\n教练分布:")
    for coach, count in coach_stats.items():
        print(f"  {coach}: {count} 人")

if __name__ == "__main__":
    students_data, failed_files = main()

    if students_data:
        generate_api_parameters(students_data)
        generate_statistics(students_data)
