#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示批量创建学员的API参数
"""

import batch_create_students
import json

def main():
    """
    主函数：显示所有学员的API创建参数并保存到文件
    """
    print("正在解析学员数据...")
    students_data, failed_files = batch_create_students.main()

    if not students_data:
        print("没有成功解析的学员数据")
        return

    # 准备输出内容
    output_lines = []
    output_lines.append(f"=== 成功解析 {len(students_data)} 个学员，生成API创建参数 ===\n")

    print(f"\n=== 成功解析 {len(students_data)} 个学员，生成API创建参数 ===\n")

    for i, student in enumerate(students_data):
        # 准备学员信息
        student_info = f"// 学员 {i+1}: {student['name']}"
        course_info = f"// 课程: {student['courseName']} (ID: {student['courseId']})"
        coach_info = f"// 教练: {student['coachName']} (ID: {student['coachId']})"
        record_info = f"// 课时记录: {student['classRecords']} 条"

        print(student_info)
        print(course_info)
        print(coach_info)
        print(record_info)

        # 添加到输出内容
        output_lines.append(student_info)
        output_lines.append(course_info)
        output_lines.append(coach_info)
        output_lines.append(record_info)

        # 构建API调用参数
        api_payload = {
            "studentInfo": {
                "name": student['name'],
                "gender": student['gender'],
                "age": student['age'],
                "phone": student['phone'],
                "email": student['email'],
                "address": student['address'],
                "parentName": student['parentName'],
                "parentPhone": student['parentPhone'],
                "campusId": student['campusId']
            },
            "courseInfoList": [
                {
                    "courseId": student['courseId'],
                    "courseName": student['courseName'],
                    "coachId": student['coachId'],
                    "coachName": student['coachName'],
                    "enrollDate": student['enrollDate'],
                    "endDate": student['expireDate'],
                    "totalHours": student['totalClasses'],
                    "consumedHours": student['consumedClasses'],
                    "remainingHours": student['remainingClasses'],
                    "status": student['status']
                }
            ]
        }

        api_json = json.dumps(api_payload, ensure_ascii=False, indent=2)
        print(api_json)
        print("\n" + "="*80 + "\n")

        # 添加到输出内容
        output_lines.append(api_json)
        output_lines.append("\n" + "="*80 + "\n")

    # 生成统计信息
    stats_lines = []
    stats_lines.append("=== 统计信息 ===")
    print("=== 统计信息 ===")

    course_stats = {}
    coach_stats = {}

    for student in students_data:
        course_name = student['courseName']
        coach_name = student['coachName']

        course_stats[course_name] = course_stats.get(course_name, 0) + 1
        coach_stats[coach_name] = coach_stats.get(coach_name, 0) + 1

    stats_lines.append("课程分布:")
    print("\n课程分布:")
    for course, count in course_stats.items():
        line = f"  {course}: {count} 人"
        print(line)
        stats_lines.append(line)

    stats_lines.append("\n教练分布:")
    print("\n教练分布:")
    for coach, count in coach_stats.items():
        line = f"  {coach}: {count} 人"
        print(line)
        stats_lines.append(line)

    summary_line1 = f"\n总计: {len(students_data)} 个学员"
    summary_line2 = f"解析失败: {len(failed_files)} 个文件"
    print(summary_line1)
    print(summary_line2)
    stats_lines.append(summary_line1)
    stats_lines.append(summary_line2)

    # 将统计信息添加到输出内容
    output_lines.extend(stats_lines)

    # 保存到文件
    output_file = "student_api_parameters.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(output_lines))
        print(f"\n✓ API参数已保存到文件: {output_file}")
    except Exception as e:
        print(f"\n✗ 保存文件失败: {e}")

if __name__ == "__main__":
    main()
