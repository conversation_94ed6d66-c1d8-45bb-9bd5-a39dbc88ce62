#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示批量创建学员的API参数
"""

import batch_create_students
import json

def main():
    """
    主函数：显示所有学员的API创建参数
    """
    print("正在解析学员数据...")
    students_data, failed_files = batch_create_students.main()
    
    if not students_data:
        print("没有成功解析的学员数据")
        return
    
    print(f"\n=== 成功解析 {len(students_data)} 个学员，生成API创建参数 ===\n")
    
    for i, student in enumerate(students_data):
        print(f"// 学员 {i+1}: {student['name']}")
        print(f"// 课程: {student['courseName']} (ID: {student['courseId']})")
        print(f"// 教练: {student['coachName']} (ID: {student['coachId']})")
        print(f"// 课时记录: {student['classRecords']} 条")
        
        # 构建API调用参数
        api_payload = {
            "studentInfo": {
                "name": student['name'],
                "gender": student['gender'],
                "age": student['age'],
                "phone": student['phone'],
                "email": student['email'],
                "address": student['address'],
                "parentName": student['parentName'],
                "parentPhone": student['parentPhone'],
                "campusId": student['campusId']
            },
            "courseInfoList": [
                {
                    "courseId": student['courseId'],
                    "courseName": student['courseName'],
                    "coachId": student['coachId'],
                    "coachName": student['coachName'],
                    "enrollDate": student['enrollDate'],
                    "endDate": student['expireDate'],
                    "totalHours": student['totalClasses'],
                    "consumedHours": student['consumedClasses'],
                    "remainingHours": student['remainingClasses'],
                    "status": student['status']
                }
            ]
        }
        
        print(json.dumps(api_payload, ensure_ascii=False, indent=2))
        print("\n" + "="*80 + "\n")
    
    # 生成统计信息
    print("=== 统计信息 ===")
    course_stats = {}
    coach_stats = {}
    
    for student in students_data:
        course_name = student['courseName']
        coach_name = student['coachName']
        
        course_stats[course_name] = course_stats.get(course_name, 0) + 1
        coach_stats[coach_name] = coach_stats.get(coach_name, 0) + 1
    
    print("\n课程分布:")
    for course, count in course_stats.items():
        print(f"  {course}: {count} 人")
    
    print("\n教练分布:")
    for coach, count in coach_stats.items():
        print(f"  {coach}: {count} 人")
    
    print(f"\n总计: {len(students_data)} 个学员")
    print(f"解析失败: {len(failed_files)} 个文件")

if __name__ == "__main__":
    main()
