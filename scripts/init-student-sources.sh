#!/usr/bin/env bash
# 初始化“学生来源”常量数据脚本
# 依赖: curl
# 使用: BASE_URL=http://host:port/lesson TOKEN=xxx ./scripts/init-student-sources.sh

set -euo pipefail

# 后端服务地址（根据实际环境修改）
BASE_URL=${BASE_URL:-"http://localhost:8080/lesson"}
API="$BASE_URL/api/constants/create"

# 可选的认证头
AUTH_HEADER=()
if [[ -n "${TOKEN:-}" ]]; then
  AUTH_HEADER=(-H "Authorization: Bearer $TOKEN")
fi

# 打印工具函数
log() { echo "[init] $1"; }

# 插入函数
create_constant() {
  local NAME="$1"          # 中文显示名称 -> constantValue
  local KEY="$2"           # 枚举值/键     -> constantKey
  local DESC="$3"          # 描述

  log "创建: $NAME ($KEY)"
  curl -sS -X POST "$API" \
    -H 'Content-Type: application/json' \
    "${AUTH_HEADER[@]}" \
    -d "{\"type\":\"STUDENT_SOURCE\",\"constantKey\":\"$KEY\",\"constantValue\":\"$NAME\",\"description\":\"$DESC\",\"status\":1}" \
    | sed 's/\\n/ /g'
  echo "\n"
}

log "开始初始化 学生来源 常量数据 -> $BASE_URL"

# 学生来源列表（名称, 枚举值, 描述）
create_constant "学员推荐"   "REFERRAL"     "学员推荐"
create_constant "美团"       "MEITUAN"      "美团"
create_constant "线下地推"   "OFFLINE_AD"   "线下广告获取"
create_constant "抖音"       "DOUYIN"       "抖音"
create_constant "上门咨询"   "WALK_IN"      "上门咨询"
create_constant "高德"       "GAODE"        "高德"
create_constant "百度"       "BAIDU"        "百度地图"
create_constant "活动转化"   "ACTIVITY"     "活动转化"
create_constant "其他"       "OTHER"        "其他来源"

log "初始化完成" 