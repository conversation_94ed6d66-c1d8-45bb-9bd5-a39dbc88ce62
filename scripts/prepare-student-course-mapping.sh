#!/usr/bin/env bash
set -euo pipefail

SOURCE_DIR=${SOURCE_DIR:-"/Users/<USER>/program/lesson/课时记"}
OUT_FILE=${OUT_FILE:-"/Users/<USER>/program/lesson/scripts/student-course-name-map.csv"}
CATALOG=${CATALOG:-"/Users/<USER>/program/lesson/scripts/course-catalog.csv"}

normalize_names() {
  local filename="$1"
  local name_part="${filename%.*}"
  name_part=$(echo "$name_part" | sed 's/[\t\r\n]//g' | tr '—–－，、_+' '-----   ' | tr '-' ' ')
  name_part=$(echo "$name_part" | sed -E 's/ +/ /g' | sed -E 's/^ +| +$//g')
  echo "$name_part"
}

main() {
  if [[ ! -d "$SOURCE_DIR" ]]; then
    echo "[error] SOURCE_DIR not found: $SOURCE_DIR" >&2
    exit 1
  fi

  tmpfile=$(mktemp)
  trap 'rm -f "$tmpfile"' EXIT

  shopt -s nullglob
  for f in "$SOURCE_DIR"/*.xls; do
    base=$(basename "$f")
    names_line=$(normalize_names "$base")
    IFS=' ' read -r -a names <<< "$names_line"
    for n in "${names[@]}"; do
      [[ ${#n} -lt 2 ]] && continue
      echo "$n" >> "$tmpfile"
    done
  done

  # unique & sort
  sort -u "$tmpfile" > "$tmpfile.sorted"

  # write file
  mkdir -p "$(dirname "$OUT_FILE")"
  {
    echo "# 姓名,课程名称"
    echo "# 参考课程清单："
    if [[ -f "$CATALOG" ]]; then
      awk -F',' 'NR>0{gsub(/^\s+|\s+$/, "", $1); gsub(/^\s+|\s+$/, "", $2); if($1!="" && $2!="") print "# " $1 " -> " $2 }' "$CATALOG"
    fi
    echo "name,course_name"
    cat "$tmpfile.sorted" | while IFS= read -r line; do
      echo "$line,"
    done
  } > "$OUT_FILE"

  echo "[ok] 生成映射模板: $OUT_FILE"
}

main "$@" 