#!/usr/bin/env bash

# 批量创建学员脚本（使用 /api/student/create 接口）
# 依赖: curl, awk, cksum, strings
# 用法示例:
#   TOKEN=xxxx CAMPUS_ID=1 COURSE_ID=1 BASE_URL=http://lesson.devtesting.top/lesson \
#   /Users/<USER>/program/lesson/scripts/batch-create-students-from-xls.sh
# 说明:
# - 默认从 /Users/<USER>/program/lesson/课时记 读取 .xls 文件名作为学员姓名
# - 仅创建学员及最小必需的课程绑定（不插入上课记录），课程ID优先从映射文件解析，否则用 COURSE_ID 兜底
# - 可通过环境变量覆盖：SOURCE_DIR, BASE_URL, CAMPUS_ID, COURSE_ID, DEFAULT_GENDER, DEFAULT_AGE, ENROLL_DATE, COURSE_STATUS
# - 可选映射文件：
#     STUDENT_COURSE_MAP（默认 /Users/<USER>/program/lesson/scripts/student-course-map.csv）格式：姓名,课程ID
#     STUDENT_COURSE_NAME_MAP（默认 /Users/<USER>/program/lesson/scripts/student-course-name-map.csv）格式：姓名,课程名称
#     COURSE_CATALOG_MAP（默认 /Users/<USER>/program/lesson/scripts/course-catalog.csv）格式：课程名称,课程ID
#     KEYWORD_COURSE_MAP（默认 /Users/<USER>/program/lesson/scripts/keyword-course-map.csv）格式：关键词,课程ID（按文件名匹配）
# - 预览模式：设置 DRY_RUN=1 仅打印将要发送的payload，不真正请求后端
# - 输出落盘：设置 OUTPUT_FILE=/path/to/file.jsonl 将每条payload以JSON Lines方式写入文件

set -euo pipefail

SOURCE_DIR=${SOURCE_DIR:-"/Users/<USER>/program/lesson/课时记"}
BASE_URL=${BASE_URL:-"http://localhost:3000/lesson"}
API_CREATE="$BASE_URL/api/student/create"
CAMPUS_ID=${CAMPUS_ID:-1}
COURSE_ID=${COURSE_ID:-1}
DEFAULT_GENDER=${DEFAULT_GENDER:-"FEMALE"}   # MALE | FEMALE
DEFAULT_AGE=${DEFAULT_AGE:-9}
ENROLL_DATE=${ENROLL_DATE:-$(date +%F)}
COURSE_STATUS=${COURSE_STATUS:-"STUDYING"}
STUDENT_COURSE_MAP=${STUDENT_COURSE_MAP:-"/Users/<USER>/program/lesson/scripts/student-course-map.csv"}
STUDENT_COURSE_NAME_MAP=${STUDENT_COURSE_NAME_MAP:-"/Users/<USER>/program/lesson/scripts/student-course-name-map.csv"}
COURSE_CATALOG_MAP=${COURSE_CATALOG_MAP:-"/Users/<USER>/program/lesson/scripts/course-catalog.csv"}
KEYWORD_COURSE_MAP=${KEYWORD_COURSE_MAP:-"/Users/<USER>/program/lesson/scripts/keyword-course-map.csv"}
DRY_RUN=${DRY_RUN:-0}
OUTPUT_FILE=${OUTPUT_FILE:-"/Users/<USER>/program/lesson/scripts/output/create-students-$(date +%Y%m%d-%H%M%S).jsonl"}

# 读取 TOKEN：优先环境变量，其次使用用户提供的默认值，再尝试从 http 文件解析
TOKEN=${TOKEN:-}
if [[ -z "${TOKEN}" ]]; then
  TOKEN="eyJhbGciOiJIUzM4NCJ9.eyJ1c2VySWQiOjIsIm9yZ0lkIjoxLCJpYXQiOjE3NTQ0NDY4OTMsImV4cCI6MTc1NTA1MTY5M30._ZT47AHCJEVgqzWemnsw424PE4SmcqQRw3O1O2mqh64WmY2wRMjdFZjfW04AnYiL"
fi
if [[ -z "${TOKEN}" ]]; then
  HTTP_FILE="/Users/<USER>/program/lesson/lesson_web_back/api-test.http"
  if [[ -f "$HTTP_FILE" ]]; then
    PARSED=$(grep -E "^@token\s*=\s*" "$HTTP_FILE" | head -n1 | sed -E 's/^@token\s*=\s*//') || true
    if [[ -n "${PARSED:-}" ]]; then
      TOKEN="$PARSED"
      echo "[info] 未提供TOKEN，已从 api-test.http 解析得到默认令牌"
    fi
  fi
fi

if [[ -z "${TOKEN}" ]]; then
  echo "[error] TOKEN 未设置，且未能从配置中解析得到。请以环境变量 TOKEN 提供。" >&2
  exit 1
fi

log() { echo "[batch-create] $1"; }

# 基于学生姓名生成稳定的11位手机号，满足 ^1[3-9]\d{9}$
# 规则：首段固定139 + 尾部8位由crc映射
generate_phone() {
  local name="$1"
  local crc
  crc=$(echo -n "$name" | cksum | awk '{print $1}')
  local last8=$(( crc % 100000000 ))
  printf "139%08d\n" "$last8"
}

# 尝试从 .xls 内容中推断课程名称，再映射到ID
# 规则：
# - 若含“大课”则课程名称=大课
# - 否则提取老师关键词(杨|武|张鲁风|肖) 与 类型关键词(一对一|1对1|一对二|1对2)，拼成 “<老师>教练一对一/二”
# - 最终在 COURSE_CATALOG_MAP 中查到对应ID
infer_course_id_from_xls() {
  local file_path="$1"
  [[ ! -f "$file_path" ]] && return 1

  # 抽取可读字符串（容错，不报错退出）
  local text
  text=$(strings "$file_path" 2>/dev/null || true)

  # 类型
  local type=""
  echo "$text" | grep -E "一对一|1对1" -q && type="一对一"
  echo "$text" | grep -E "一对二|1对2" -q && type="一对二"
  echo "$text" | grep -E "大课" -q && type="大课"

  # 直接命中大课
  if [[ "$type" == "大课" ]]; then
    if [[ -f "$COURSE_CATALOG_MAP" ]]; then
      local cid
      cid=$(awk -F',' -v c="大课" '{gsub(/^\s+|\s+$/, "", $1); gsub(/^\s+|\s+$/, "", $2); if($1==c){print $2; exit}}' "$COURSE_CATALOG_MAP")
      [[ -n "${cid:-}" ]] && echo "$cid" && return 0
    fi
  fi

  # 老师关键词
  local teacher=""
  for kw in 张鲁风 武 杨 肖; do
    echo "$text" | grep -E "$kw" -q && { teacher="$kw"; break; }
  done

  if [[ -n "$teacher" && -n "$type" ]]; then
    local cname
    if [[ "$type" == "一对一" ]]; then
      cname="${teacher}教练一对一"
    else
      cname="${teacher}教练一对二"
    fi
    if [[ -f "$COURSE_CATALOG_MAP" ]]; then
      local cid
      cid=$(awk -F',' -v c="$cname" '{gsub(/^\s+|\s+$/, "", $1); gsub(/^\s+|\s+$/, "", $2); if($1==c){print $2; exit}}' "$COURSE_CATALOG_MAP")
      if [[ -n "${cid:-}" ]]; then
        echo "$cid"; return 0
      fi
    fi
  fi

  return 1
}

# 解析课程ID：
# 1) 学生->课程ID 表
# 2) 学生->课程名称 + 课程名称->课程ID
# 3) 从.xls内容推断(老师+类型 / 大课)
# 4) 文件名关键词->课程ID
# 5) 默认 COURSE_ID
resolve_course_id() {
  local student_name="$1"
  local file_base="$2"
  local file_path="$3"

  # 1) 学生->课程ID
  if [[ -f "$STUDENT_COURSE_MAP" ]]; then
    local line
    line=$(awk -F',' -v n="$student_name" '{gsub(/^\s+|\s+$/, "", $1); gsub(/^\s+|\s+$/, "", $2); if($1==n){print $0; exit}}' "$STUDENT_COURSE_MAP" || true)
    if [[ -n "${line:-}" ]]; then
      local cid
      cid=$(echo "$line" | awk -F',' '{print $2}' | tr -d ' ')
      if [[ "$cid" =~ ^[0-9]+$ ]]; then
        echo "$cid"; return 0
      fi
    fi
  fi

  # 2) 学生->课程名称 + 课程名称->课程ID
  if [[ -f "$STUDENT_COURSE_NAME_MAP" && -f "$COURSE_CATALOG_MAP" ]]; then
    local cname
    cname=$(awk -F',' -v n="$student_name" '{gsub(/^\s+|\s+$/, "", $1); sub(/\r$/, "", $2); if($1==n){print $2; exit}}' "$STUDENT_COURSE_NAME_MAP" || true)
    if [[ -n "${cname:-}" ]]; then
      cname=$(echo "$cname" | sed -E 's/^\s+|\s+$//g')
      local cid
      cid=$(awk -F',' -v c="$cname" '{gsub(/^\s+|\s+$/, "", $1); gsub(/^\s+|\s+$/, "", $2); if($1==c){print $2; exit}}' "$COURSE_CATALOG_MAP" || true)
      if [[ -n "${cid:-}" && "$cid" =~ ^[0-9]+$ ]]; then
        echo "$cid"; return 0
      fi
    fi
  fi

  # 3) 从.xls内容推断
  if cid=$(infer_course_id_from_xls "$file_path"); then
    echo "$cid"; return 0
  fi

  # 4) 文件名关键词
  if [[ -f "$KEYWORD_COURSE_MAP" ]]; then
    while IFS=',' read -r kw cid; do
      kw=$(echo "$kw" | sed -E 's/^\s+|\s+$//g');
      cid=$(echo "$cid" | sed -E 's/^\s+|\s+$//g');
      [[ -z "$kw" || -z "$cid" ]] && continue
      if echo "$file_base" | grep -q "$kw"; then
        if [[ "$cid" =~ ^[0-9]+$ ]]; then
          echo "$cid"; return 0
        fi
      fi
    done < "$KEYWORD_COURSE_MAP"
  fi

  # 5) 默认
  echo "$COURSE_ID"
}

# 将文件名转为候选姓名数组（去扩展名 + 以常见分隔符拆分）
split_names() {
  local filename="$1"
  local name_part="${filename%.*}"
  name_part=$(echo "$name_part" | sed 's/[\t\r\n]//g' | tr '—–－，、_+' '-----   ' | tr '-' ' ')
  name_part=$(echo "$name_part" | sed -E 's/ +/ /g' | sed -E 's/^ +| +$//g')
  [[ -z "$name_part" ]] && return 1
  echo "$name_part"
}

write_payload() {
  local payload_json="$1"
  mkdir -p "$(dirname "$OUTPUT_FILE")"
  echo "$payload_json" | tr -d '\n' >> "$OUTPUT_FILE"
  echo "" >> "$OUTPUT_FILE"
}

create_student() {
  local name="$1"
  local phone="$2"
  local course_id="$3"

  local payload
  payload=$(cat <<JSON
{
  "studentInfo": {
    "name": "${name}",
    "gender": "${DEFAULT_GENDER}",
    "age": ${DEFAULT_AGE},
    "phone": "${phone}",
    "campusId": ${CAMPUS_ID}
  },
  "courseInfoList": [
    {
      "courseId": ${course_id},
      "enrollDate": "${ENROLL_DATE}",
      "status": "${COURSE_STATUS}"
    }
  ]
}
JSON
)

  log "创建学员: ${name} (${phone}) -> courseId=${course_id}"

  write_payload "$payload"

  if [[ "$DRY_RUN" == "1" ]]; then
    echo "[dry-run] POST $API_CREATE"
    echo "[dry-run] Payload: $payload"
    return 0
  fi

  local http_code=""
  if ! http_code=$(curl -sS -o /tmp/create_student_resp.json -w "%{http_code}" \
    -X POST "$API_CREATE" \
    -H "Content-Type: application/json" \
    -H "Authorization: ${TOKEN}" \
    --data "$payload"); then
    echo "[error] 网络请求失败" >&2
    return 1
  fi

  if [[ "$http_code" != "200" ]]; then
    echo "[error] 接口HTTP状态码: $http_code，响应: $(cat /tmp/create_student_resp.json)" >&2
    return 1
  fi

  echo "[ok] 响应: $(cat /tmp/create_student_resp.json)"
}

main() {
  if [[ ! -d "$SOURCE_DIR" ]]; then
    echo "[error] SOURCE_DIR 不存在: $SOURCE_DIR" >&2
    exit 1
  fi

  mkdir -p "$(dirname "$OUTPUT_FILE")"
  : > "$OUTPUT_FILE"
  echo "[info] 输出文件: $OUTPUT_FILE"

  shopt -s nullglob
  local files=("$SOURCE_DIR"/*.xls)
  if [[ ${#files[@]} -eq 0 ]]; then
    echo "[warn] 未在 $SOURCE_DIR 找到任何 .xls 文件" >&2
  fi

  local total_created=0
  for f in "${files[@]}"; do
    local base
    base=$(basename "$f")
    local names_line
    names_line=$(split_names "$base") || continue

    IFS=' ' read -r -a names <<< "$names_line"
    for name in "${names[@]}"; do
      [[ ${#name} -lt 2 ]] && continue
      local phone
      phone=$(generate_phone "$name")
      local cid
      cid=$(resolve_course_id "$name" "$base" "$f")
      if create_student "$name" "$phone" "$cid"; then
        total_created=$((total_created + 1))
      fi
      sleep 0.05
    done
  done

  log "处理完成，新增学员数量: $total_created"
  echo "[info] 已写入: $OUTPUT_FILE"
}

main "$@" 